import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addCustomBillingFields() {
  let connection: mysql.Connection | null = null;

  try {
    console.log('🚀 Adding custom billing fields to subscription_plans table...');

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'hifnf_db',
    });

    console.log('✅ Connected to database');

    // Check if columns already exist
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'subscription_plans' 
      AND COLUMN_NAME IN ('customBillingMonths', 'customBillingYears')
    `, [process.env.DB_NAME || 'hifnf_db']);

    const existingColumns = (columns as any[]).map(col => col.COLUMN_NAME);

    // Add customBillingMonths column if it doesn't exist
    if (!existingColumns.includes('customBillingMonths')) {
      console.log('Adding customBillingMonths column...');
      await connection.execute(`
        ALTER TABLE subscription_plans 
        ADD COLUMN customBillingMonths INT NULL 
        COMMENT 'Custom number of months for billing cycle'
      `);
      console.log('✅ Added customBillingMonths column');
    } else {
      console.log('ℹ️ customBillingMonths column already exists');
    }

    // Add customBillingYears column if it doesn't exist
    if (!existingColumns.includes('customBillingYears')) {
      console.log('Adding customBillingYears column...');
      await connection.execute(`
        ALTER TABLE subscription_plans 
        ADD COLUMN customBillingYears INT NULL 
        COMMENT 'Custom number of years for billing cycle'
      `);
      console.log('✅ Added customBillingYears column');
    } else {
      console.log('ℹ️ customBillingYears column already exists');
    }

    console.log('🎉 Custom billing fields migration completed successfully!');

  } catch (error) {
    console.error('❌ Error adding custom billing fields:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('✅ Database connection closed');
    }
  }
}

// Run the migration
if (require.main === module) {
  addCustomBillingFields()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export default addCustomBillingFields;
